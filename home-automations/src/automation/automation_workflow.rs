use std::collections::{HashMap, HashSet};

use crate::entity::entity_state::EntityState;
use crate::event_bus::events::DeviceEvent;
use crate::values_objects::Id;

#[derive(Debug, Clone)]
pub enum WorkflowTriggerType {
    StateChanged {
        entity_id: Id,
        from: EntityState,
        to: EntityState,
    },
}

impl WorkflowTriggerType {
    /// Check if a component change matches the expected state transition
    fn matches_component_change(
        &self,
        expected_from: &EntityState,
        expected_to: &EntityState,
        change: &crate::entity::entity_state::EntityStateChangeResult,
    ) -> bool {
        let change_type_id = change.from.as_ref().type_id();

        // Check if the expected 'from' state has a matching component
        let from_matches = if let Some(expected_component) = expected_from.get_component_by_type_id(change_type_id) {
            // For now, we'll use a simple debug string comparison
            // This is not ideal but works for basic cases
            format!("{:?}", expected_component) == format!("{:?}", change.from)
        } else {
            false
        };

        // Check if the expected 'to' state has a matching component
        let to_matches = if let Some(expected_component) = expected_to.get_component_by_type_id(change_type_id) {
            format!("{:?}", expected_component) == format!("{:?}", change.to)
        } else {
            false
        };

        from_matches && to_matches
    }

    pub fn can_trigger_from_event(&self, event: &DeviceEvent) -> bool {
        match self {
            WorkflowTriggerType::StateChanged {
                entity_id,
                from,
                to,
            } => match event {
                DeviceEvent::StateChangedEvent {
                    entity_id: event_entity_id,
                    changes,
                } => {
                    if event_entity_id != entity_id {
                        return false;
                    }

                    for change in changes {
                        // Check if this change matches our expected state transition
                        if self.matches_component_change(from, to, change) {
                            return true;
                        }
                    }

                    false
                }
            },
        }
    }
}

#[derive(Debug, Clone)]
pub enum WorkflowActionType {
    TurnOnEntity { entity_id: Id },
    TurnOffEntity { entity_id: Id },
}

#[derive(Debug, Clone)]
pub enum WorkflowNodeType {
    Trigger(WorkflowTriggerType),
    Action(WorkflowActionType),
}

#[derive(Debug)]
pub enum WorkflowExecutionState {
    
    Triggered,
    Declined,
    Waiting,
    Completed,
    Failed,
}

#[derive(Debug)]
pub struct WorkflowNode {
    pub id: Id,
    pub workflow_type: WorkflowNodeType,
    pub execution_state: WorkflowExecutionState,
}

#[derive(Debug)]
pub struct Workflow {
    nodes: HashMap<Id, WorkflowNode>,
    trigger_nodes: HashSet<Id>,
    edges: HashMap<Id, HashSet<Id>>,
}

impl Workflow {
    pub fn new() -> Self {
        Self {
            nodes: HashMap::new(),
            trigger_nodes: HashSet::new(),
            edges: HashMap::new(),
        }
    }

    pub fn add_node(&mut self, node: WorkflowNode) {
        let node_id = node.id.clone();
        let is_trigger = self.is_trigger_node(&node);

        self.nodes.insert(node_id.clone(), node);

        if is_trigger {
            self.trigger_nodes.insert(node_id);
        }
    }

    fn is_trigger_node(&self, node: &WorkflowNode) -> bool {
        matches!(node.workflow_type, WorkflowNodeType::Trigger(_))
    }

    pub fn add_edge(&mut self, from_workflow_node: &WorkflowNode, to_workflow_node: &WorkflowNode) {
        self.edges
            .entry(from_workflow_node.id.clone())
            .or_insert_with(HashSet::new)
            .insert(to_workflow_node.id.clone());
    }

    pub fn can_trigger_from_event(&mut self, event: &DeviceEvent) -> bool {
        let entity_id = match event {
            DeviceEvent::StateChangedEvent { entity_id, .. } => entity_id,
        };

        if !self.trigger_nodes.contains(entity_id) {
            return false;
        }

        let workflow_node = self.nodes.get(entity_id).unwrap();

        match &workflow_node.workflow_type {
            WorkflowNodeType::Trigger(trigger_type) => trigger_type.can_trigger_from_event(event),
            _ => false,
        }
    }

    pub fn execute_from_event(&mut self, event: DeviceEvent) {
        if !self.can_trigger_from_event(&event) {
            return;
        }

        let entity_id = match event {
            DeviceEvent::StateChangedEvent { entity_id, .. } => entity_id,
        };

        let next_nodes = self.edges[&entity_id].clone();

        for node_id in next_nodes {
            let node = self.nodes.get(&node_id).unwrap();
            
            self.execute_node(&node);
        }
    }

    fn execute_node(&self, node: &WorkflowNode) {
        match &node.workflow_type {
            WorkflowNodeType::Action(action_type) => {
                print!("Executing action: {:?}\n", action_type);
            }
            _ => {}
        }
    }
}
