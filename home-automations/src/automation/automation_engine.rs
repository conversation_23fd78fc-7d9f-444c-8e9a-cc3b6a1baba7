use std::sync::Arc;
use tokio::sync::Mutex;

use crate::{
    automation::Automation,
    event_bus::{EventBus, events::DeviceEvent},
};

#[derive(Debug)]
pub struct AutomationEngine {
    automations: Vec<Automation>,
    event_bus: Arc<EventBus>,
}

impl AutomationEngine {
    pub fn new(event_bus: Arc<EventBus>) -> Self {
        Self {
            automations: Vec::new(),
            event_bus,
        }
    }

    pub async fn start(&mut self) -> tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON><()> {
        let mut device_event_receiver = self.event_bus.subscribe_to_device_events().await;
        let automations = Arc::new(Mutex::new(std::mem::take(&mut self.automations)));

        let automations_clone = Arc::clone(&automations);
        tokio::spawn(async move {
            while let Ok(event) = device_event_receiver.recv().await {
                let mut automations_guard = automations_clone.lock().await;
                // TODO: handle errors
                for automation in automations_guard.iter_mut() {
                    if automation.can_trigger_from_event(&event) {
                        automation.execute_from_event(&event);
                    }
                }
            }
        })
    }

    pub fn add_automation(&mut self, automation: Automation) -> &mut Self {
        self.automations.push(automation);

        self
    }

    pub fn remove_automation(&mut self, automation: Automation) -> &mut Self {
        self.automations.retain(|a| a.name != automation.name);

        self
    }


}
