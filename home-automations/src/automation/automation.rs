use crate::{automation::Workflow, event_bus::events::DeviceEvent, values_objects::Id};

#[derive(Debug)]
pub struct Automation {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub workflow: Workflow,
}

impl Automation {
    pub fn new(id: Id, name: String, description: Option<String>) -> Self {
        Self {
            id,
            name,
            description,
            workflow: Workflow::new(),
        }
    }

    pub fn can_trigger_from_event(&mut self, event: &DeviceEvent) -> bool {
        self.workflow.can_trigger_from_event(event)
    }

    pub fn execute_from_event(&mut self, event: &DeviceEvent) {
        self.workflow.execute_from_event(event.clone())
    }
}