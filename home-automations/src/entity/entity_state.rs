use std::{
    any::{Any, TypeId},
    fmt::Debug,
};

pub trait Component: Debug + Any + Send + Sync + 'static {
    fn as_any(&self) -> &dyn Any;
    fn as_any_mut(&mut self) -> &mut dyn Any;
    fn clone_box(&self) -> Box<dyn Component>;
}

#[derive(Debug)]
pub struct EntityState {
    components: Vec<Box<dyn Component>>,
}

impl Clone for EntityState {
    fn clone(&self) -> Self {
        Self {
            components: self.components.iter().map(|c| c.clone_box()).collect(),
        }
    }
}

impl EntityState {
    pub fn new() -> Self {
        Self {
            components: Vec::new(),
        }
    }

    pub fn from_components(components: Vec<Box<dyn Component>>) -> Self {
        Self { components }
    }

    pub fn add_component(&mut self, component: Box<dyn Component>) -> &mut Self {
        self.components.push(component);
        self
    }

    pub fn get_component<T: Component>(&self) -> Option<&T> {
        self.components
            .iter()
            .find(|component| component.as_ref().type_id() == TypeId::of::<T>())
            .and_then(|component| component.as_any().downcast_ref::<T>())
    }

    pub fn get_component_mut<T: Component>(&mut self) -> Option<&mut T> {
        self.components
            .iter_mut()
            .find(|component| component.as_ref().type_id() == TypeId::of::<T>())
            .and_then(|component| component.as_any_mut().downcast_mut::<T>())
    }

    pub fn len(&self) -> usize {
        self.components.len()
    }

    /// Get a component by its TypeId for comparison purposes
    pub fn get_component_by_type_id(&self, type_id: std::any::TypeId) -> Option<&Box<dyn Component>> {
        self.components
            .iter()
            .find(|component| component.as_ref().type_id() == type_id)
    }

    pub fn update_state(&mut self, new_state: EntityState) -> Vec<EntityStateChangeResult> {
        if new_state.len() == 0 {
            return Vec::new();
        }

        let mut change_result = Vec::new();

        for new_component in new_state.components {
            let component_type_id = new_component.as_ref().type_id();

            // Find existing component with the same type
            let existing_index = self.components
                .iter()
                .position(|c| c.as_ref().type_id() == component_type_id);

            if let Some(index) = existing_index {
                // Clone the new component before replacing
                let new_component_clone = new_component.clone_box();

                // Replace existing component and record the change
                let old_component = std::mem::replace(&mut self.components[index], new_component);
                change_result.push(EntityStateChangeResult {
                    from: old_component,
                    to: new_component_clone,
                });
            }
        }

        change_result
    }
}

#[derive(Debug)]
pub struct EntityStateChangeResult {
    pub from: Box<dyn Component>,
    pub to: Box<dyn Component>,
}

impl Clone for EntityStateChangeResult {
    fn clone(&self) -> Self {
        Self {
            from: self.from.clone_box(),
            to: self.to.clone_box(),
        }
    }
}