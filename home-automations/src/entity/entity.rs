use crate::{
    entity::{Component, EntityState},
    values_objects::Id,
};

#[derive(Debug)]
pub struct Entity {
    pub id: Id,
    pub name: String,
    pub description: String,
    pub state: EntityState,
}

impl Entity {
    pub fn new(id: Id, name: String, description: String) -> Self {
        Self {
            id,
            name,
            description,
            state: EntityState::new(),
        }
    }

    pub fn add_component<T: Component + 'static>(&mut self, component: Box<T>) -> &mut Self {
        self.state.add_component(component);
        self
    }
}