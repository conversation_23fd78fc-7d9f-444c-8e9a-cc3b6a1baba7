use std::any::Any;

use crate::values_objects::Color;

use super::Component;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct OnOffComponent {
    pub is_on: bool,
}

impl Component for OnOffComponent {
    fn as_any(&self) -> &dyn Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }

    fn clone_box(&self) -> Box<dyn Component> {
        Box::new(self.clone())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ButtonPressComponent {
    pub button_id: String,
}

impl Component for ButtonPressComponent {
    fn as_any(&self) -> &dyn Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }

    fn clone_box(&self) -> Box<dyn Component> {
        Box::new(self.clone())
    }
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub struct ButtonLongPressComponent {
    pub button_id: String,
}

impl Component for ButtonLongPressComponent {
    fn as_any(&self) -> &dyn Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }

    fn clone_box(&self) -> Box<dyn Component> {
        Box::new(self.clone())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct BrightnessComponent {
    pub brightness: u8,
}

impl Component for BrightnessComponent {
    fn as_any(&self) -> &dyn Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }

    fn clone_box(&self) -> Box<dyn Component> {
        Box::new(self.clone())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ColorComponent {
    pub color: Color,
}

impl Component for ColorComponent {
    fn as_any(&self) -> &dyn Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }

    fn clone_box(&self) -> Box<dyn Component> {
        Box::new(self.clone())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ColorTemperatureComponent {
    pub color_temperature: u16,
}

impl Component for ColorTemperatureComponent {
    fn as_any(&self) -> &dyn Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn Any {
        self
    }

    fn clone_box(&self) -> Box<dyn Component> {
        Box::new(self.clone())
    }
}
