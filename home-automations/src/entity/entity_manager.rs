use crate::{
    entity::{Component, Entity, EntityState},
    event_bus::{EventBus, events::DeviceEvent},
    values_objects::Id,
};
use home_automations::Result;
use std::{collections::HashMap, sync::Arc};

pub struct EntityManager {
    entities: HashMap<Id, Entity>,
    event_bus: Arc<EventBus>,
}

impl EntityManager {
    pub fn new(event_bus: Arc<EventBus>) -> Self {
        Self {
            entities: HashMap::new(),
            event_bus,
        }
    }

    pub fn register_entity(&mut self, entity: Entity) {
        self.entities.insert(entity.id.clone(), entity);
    }

    pub fn get_by_id(&self, id: Id) -> Option<&Entity> {
        self.entities.get(&id)
    }

    pub fn remove_entity_by_id(&mut self, id: Id) {
        self.entities.remove(&id);
    }

    pub fn get_entities_with_component<T: Component>(&self) -> Vec<&Entity> {
        self.entities
            .values()
            .filter(|e| e.state.get_component::<T>().is_some())
            .collect()
    }

    pub async fn update_entity_state(&mut self, id: Id, state: EntityState) -> Result<()> {
        if let Some(entity) = self.entities.get_mut(&id) {
            let change_result = entity.state.update_state(state);
            
            return self
                .event_bus
                .publish_device_event(DeviceEvent::StateChangedEvent {
                    entity_id: id,
                    changes: change_result,
                })
                .await;
        }
        Ok(())
    }
}
