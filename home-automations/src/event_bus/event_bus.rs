use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};
use std::collections::HashMap;

use crate::event_bus::events::{AutomationEvent, DeviceEvent};
use home_automations::Result;


pub type DeviceEventHandler = Arc<dyn Fn(DeviceEvent) -> Result<()> + Send + Sync>;
pub type AutomationEventHandler = Arc<dyn Fn(AutomationEvent) -> Result<()> + Send + Sync>;

#[derive(Clone)]
pub struct EventBus {
    device_sender: broadcast::Sender<DeviceEvent>,
    automation_sender: broadcast::Sender<AutomationEvent>,
    device_handlers: Arc<RwLock<HashMap<String, DeviceEventHandler>>>,
    automation_handlers: Arc<RwLock<HashMap<String, AutomationEventHandler>>>,
}

impl EventBus {
    pub fn new() -> Self {
        let (device_sender, _) = broadcast::channel(1000);
        let (automation_sender, _) = broadcast::channel(1000);
        
        Self {
            device_sender,
            automation_sender,
            device_handlers: Arc::new(RwLock::new(HashMap::new())),
            automation_handlers: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    pub async fn publish_device_event(&self, event: DeviceEvent) -> Result<()> {
        // Broadcast to all subscribers
        if let Err(e) = self.device_sender.send(event.clone()) {
            // tracing::warn!("Failed to broadcast device event: {}", e);
        }
        
        // Call registered handlers
        let handlers = self.device_handlers.read().await;
        for handler in handlers.values() {
            if let Err(e) = handler(event.clone()) {
                // tracing::error!("Device event handler failed: {}", e);
            }
        }
        
        Ok(())
    }
    
    pub async fn publish_automation_event(&self, event: AutomationEvent) -> Result<()> {
        // Broadcast to all subscribers
        if let Err(e) = self.automation_sender.send(event.clone()) {
            // tracing::warn!("Failed to broadcast automation event: {}", e);
        }
        
        // Call registered handlers
        let handlers = self.automation_handlers.read().await;
        for handler in handlers.values() {
            if let Err(e) = handler(event.clone()) {
                // tracing::error!("Automation event handler failed: {}", e);
            }
        }
        
        Ok(())
    }
    
    pub async fn subscribe_to_device_events(&self) -> broadcast::Receiver<DeviceEvent> {
        self.device_sender.subscribe()
    }
    
    pub async fn subscribe_to_automation_events(&self) -> broadcast::Receiver<AutomationEvent> {
        self.automation_sender.subscribe()
    }
    
    pub async fn register_device_handler(&self, name: String, handler: DeviceEventHandler) {
        let mut handlers = self.device_handlers.write().await;
        handlers.insert(name, handler);
    }
    
    pub async fn register_automation_handler(&self, name: String, handler: AutomationEventHandler) {
        let mut handlers = self.automation_handlers.write().await;
        handlers.insert(name, handler);
    }
    
    pub async fn unregister_device_handler(&self, name: &str) {
        let mut handlers = self.device_handlers.write().await;
        handlers.remove(name);
    }
    
    pub async fn unregister_automation_handler(&self, name: &str) {
        let mut handlers = self.automation_handlers.write().await;
        handlers.remove(name);
    }
}

impl std::fmt::Debug for EventBus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("EventBus")
            .field("device_sender", &self.device_sender)
            .field("automation_sender", &self.automation_sender)
            .field("device_handlers", &"<function handlers>")
            .field("automation_handlers", &"<function handlers>")
            .finish()
    }
}